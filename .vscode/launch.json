{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {   // Requires arm-none-eabi-gdb to be aliased to gdb-multiarch
            // Also requires running `make debug` in a terminal prior to launch
            "name": "Debug STM32L4 (OpenOCD)",
            "type": "cortex-debug",
            "request": "launch",
            "servertype": "external",
            "executable": "${workspaceFolder}/original_source/images/pelagic_vms.elf",
            "cwd": "${workspaceFolder}",
            "device": "STM32L431CC",                         // Your specific part
            "interface": "swd",                              // Most STM32 boards use SWD
            "runToEntryPoint": "main",                       // Optional, run to main
            "svdFile": "${workspaceFolder}/STM32L4x1.svd",   // Optional SVD for peripheral view
            "configFiles": [
                "${workspaceFolder}/debug.cfg",   // OpenOCD config file
            ],
            "postRestartCommands": [
              "monitor reset halt"
            ],
            "preLaunchTask": "Build",
            "gdbTarget": "localhost:2331",                     // GDB server port
            "rtos": "FreeRTOS",
            "swoConfig": {
                "enabled": true,
                "source": "socket",
                "swoPort": "2332",
                "swoFrequency": 4000000,
                "cpuFrequency": 4000000,
                "decoderBytes": 2,
                "decoders": [
                    {
                        "type": "console",
                        "label": "SWO",
                        "port": 0,
                        "filter": "",
                        "showTimestamp": true,
                        "showTimestampInLocalTime": false
                    }
                ]
            }
        },
    ]
}
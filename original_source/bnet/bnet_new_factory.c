
#include "pelagic.h"
#include "alarm.h"
#include "factory_test.h"
#include "system_file.h"
#include "mcu_sleep.h"
#include "power.h"
#include "provision.h"
#include "rtc_api.h"
#include <stdarg.h>

// Minimal factory test header without bnet dependencies
typedef enum {
        NEW_FT_RESPONSE_UNKNOWN        = 0x00,
        NEW_FT_RESPONSE_PASS           = 0x10,
        NEW_FT_RESPONSE_FAIL           = 0x20,
        NEW_FT_RESPONSE_TESTS_RUNNING  = 0x30,
        NEW_FT_RESPONSE_VOLTAGE_REPORT = 0x40,
        NEW_FT_RESPONSE_NOT_IN_FT_MODE = 0x50,
} bnet_new_ft_response_code_t;

int RESPONSE_SEND_TIME = 10; // Try sending for 10 seconds
int RESPONSE_ACK_TIME = 1000; // Wait for 1000ms for an ACK each time
int DEEP_SLEEP_SECONDS = 4;

int FACTORY_TEST_PERIOD = 14 * DAY;

int MINIMIM_SOLAR_TIME = 5 * MINUTE;
int MINIMIM_NO_RADIO_TIME = 5 * MINUTE;

bool in_test_mode = false;
bool test_in_progress = false;

extern int vsprintf(char *buf, const char *fmt, va_list args);
bnet_new_ft_response_code_t test_result = NEW_FT_RESPONSE_UNKNOWN;
osThreadDef(factory_test_thread, osPriorityNormal, 1, 0);
osThreadId factory_test_tid;

char *last_message;
uint8_t last_message_size;

#ifndef OLD_FACTORY
void
ft_update(const char *message, ...)
{
        va_list args;
        last_message = (char*)the_buffer.test;

        va_start(args, message);
        last_message_size = vsprintf(last_message, message, args);
        va_end(args);

        uart_printf("= %s\n", last_message);
}
#endif

// Simplified factory test trigger - no network communication
void
factory_test_trigger()
{
        in_test_mode = true;
        test_in_progress = true;
        uart_printf("Factory test triggered\n");
}

        case BNET_TYPE_NEW_FT_ENTER_DEEP_SLEEP:
                if (in_test_mode) {
                        osSignalWait(0, 1000); // Give the radio thread time to send ACK
                        mcu_sleep(DEEP_SLEEP_SECONDS);
                } else {
                        bnet_new_factory_respond(NEW_FT_RESPONSE_NOT_IN_FT_MODE, no_data);
                }
                break;

        case BNET_TYPE_NEW_FT_START_SELF_TEST:
                if (in_test_mode && !test_in_progress) {
                        test_result = NEW_FT_RESPONSE_TESTS_RUNNING;
                        test_in_progress = true;
                        bnet_new_factory_respond(test_result, no_data);
                } else {
                        bnet_new_factory_respond(NEW_FT_RESPONSE_NOT_IN_FT_MODE, no_data);
                }
                break;

        case BNET_TYPE_NEW_FT_SELF_TEST_STATUS:
                if (in_test_mode) {
                        include_message = true;
                        bnet_new_factory_respond(test_result, no_data);
                        include_message = false;
                } else {
                        bnet_new_factory_respond(NEW_FT_RESPONSE_NOT_IN_FT_MODE, no_data);
                }
                break;

        case BNET_TYPE_NEW_FT_EXIT_TEST_MODE:
                if (in_test_mode) {
                        in_test_mode = false;
                        last_message_size = 0;

                        // osSignalSet(factory_test_tid, FT_SIGNAL_ABORT);
                        // thread_status = osThreadTerminate (factory_test_tid);
                        // if (thread_status == osOK) {
                        //     uart_printf("\n\nTerminated Test thread!!\n\n\n");
                        //     // osSignalWait(0, 1000);
                        //     // osThreadDef(factory_test_thread, osPriorityNormal, 1, 0);
                        //     // factory_test_tid = osThreadCreate(osThread(factory_test_thread), NULL);
                        // }
                        // else {
                        //     uart_printf("\n\nFailed to terminate thread!!\n\n\n");
                        // }
                }
                break;

        case BNET_TYPE_NEW_FT_SHIP:
                in_test_mode = false;
                osSignalWait(0, 1000); // Give the radio thread time to send ACK
                provision_shipping();

                provision_sleep = SHIPPING_SLEEP;
                osSignalSet(reboot_tid, REBOOT_SIGNAL_NORMAL); // Reboot the board

                break;
        }
}

void
factory_test_thread(void const *arg)
{
        for (;;) {
                if (test_in_progress) {
                        test_result = make_test_go_now();
                        test_in_progress = false;
                        in_test_mode = false;
                } else {
                        osSignalWait(0, 1000);
                }
        }
}


uint8_t
make_test_go_now()
{
        const ft_device_t *device;
        device = ft_devices;

        // Run individual tests
        for (int i = 0; i < ft_device_count; i++, device++) {
                if (device->test() != FT_SUCCESS)
                        return NEW_FT_RESPONSE_FAIL;
        }

        return NEW_FT_RESPONSE_PASS;
}


void
bnet_new_factory_request_ack(uint8_t type, imei_t target_uid)
{
        bnet_new_ft_request_ack_t *ack = (bnet_new_ft_request_ack_t *)bnet_send_buffer;

        ack->type = type | BNET_ACK;
        imei_copy(ack->from_imei, board_info_imei);
        uid_copy(ack->to_uid, target_uid);

        radio_send(ack, sizeof(bnet_new_ft_request_ack_t));
}

void
bnet_new_factory_respond(uint8_t code, uint16_t data[2])
{
        int bytes;
        uint32_t now, last_sent;
        volatile bool buzzer;
        alarm_t timeout;
        bnet_new_ft_response_t *response = (bnet_new_ft_response_t *)bnet_send_buffer;
        bnet_new_ft_response_ack_t *ack = (bnet_new_ft_response_ack_t *)bnet_receive_buffer;

        last_sent = 0;
        alarm_start_set(&timeout, RESPONSE_SEND_TIME, &buzzer);
        for (;;) {
                now = clock_read();

                if (last_sent != now) {
                        response->type = BNET_TYPE_NEW_FT_RESPONSE;
                        response->response_code = code;
                        response->data[0] = data[0];
                        response->data[1] = data[1];
                        imei_copy(response->from_imei, board_info_imei);
                        uid_copy(response->to_uid, *factory_uid);
                        if (include_message) {
                                response->message_size = last_message_size;
                                memcpy(response->message, last_message, last_message_size);
                                // response->message[last_message_size] = '\0';
                        } else {
                                response->message_size = 0;
                        }

                        radio_send(response, sizeof(bnet_new_ft_response_t) + last_message_size);
                        last_sent = now;
                }

                bytes = radio_read(ack, RESPONSE_ACK_TIME, NULL);

                if (buzzer) {
                        uart_printf("! timeout on ack\n");
                        break;
                }

                // I'm not sure why I can't call imei_match_me...
                bool match_imei = board_info_have_imei && imei_match(ack->to_imei, board_info_imei);
                if (bytes == sizeof(bnet_new_ft_response_ack_t) && ack->type == (BNET_TYPE_NEW_FT_RESPONSE | BNET_ACK) && match_imei) {
                        alarm_cancel(&timeout);
                        break;
                }
        }

        uart_printf("\n");
}

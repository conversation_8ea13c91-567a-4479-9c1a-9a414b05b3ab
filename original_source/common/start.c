/** @{

    @ingroup common
    @file

    @brief  low level startup
*/

#include "pelagic.h"
#include "wait_api.h"
#include "mbed_error.h"
#include "gpio_api.h"

// defined by the linker script
extern uint32_t __bss_start__;
extern uint32_t __bss_end__;

extern void SystemInit();
extern osThreadDef_t os_thread_def_main;

/**
    @brief  called just after the ResetHandler
    @note   The ResetHandler copies any RAM based code from flash into
            RAM and then jumps here. The following is done:
            - BSS section is zeroed out
            - The MCU clocks are initialized
            - RTX is initialized but not started
            - The main() thread is created
            - RTX is started and the main() thread is run.
*/

volatile bool board_initted = false;

void
_start(void)
{
        // uint32_t *dst;

        // Zero out the BSS section
        // for (dst = &__bss_start__; dst < &__bss_end__; )
        //         *(dst++) = 0;

        // Init the clocks
        // SystemInit();
        //! PORT: modified startup_stm32l431xx.S to call SystemInit

#ifdef __USING_LIBC__
        // if libc is ever included again, need to set it up
        __libc_init_array();
#endif

        // Initialize RTOS
        osKernelInitialize();

        board_initted = true;
        // Setup create the main thread, and launch everything..
        osThreadCreate(&os_thread_def_main, NULL);
        osKernelStart();

        // Should never get here..
        for(;;) {
                __asm volatile("NOP");
        }
}

/** @} */
